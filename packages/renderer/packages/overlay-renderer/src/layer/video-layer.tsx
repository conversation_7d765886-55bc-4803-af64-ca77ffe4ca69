import React, { useEffect, useMemo, useRef, useState } from 'react'
import { continueRender, delayRender, Video } from 'remotion'
import * as THREE from 'three'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import { useFrame, useThree } from '@react-three/fiber'
import { parseMedia } from '@remotion/media-parser'
import { RenderableOverlay, VideoOverlay } from '@app/shared/types/overlay'
import { useRenderContext } from '../render.context'
import { getProgressiveOverlayProps } from '../utils/getProgressiveOverlayProps'

// 基础顶点着色器
const VERTEX_SHADER = /* glsl */ `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`

// 片段着色器 - 支持基于 UV 的裁剪（u_crop: x, y, width, height，均为 0~1）
const FRAGMENT_SHADER = /* glsl */ `
  precision highp float;
  varying vec2 vUv;

  uniform sampler2D u_tex;
  uniform float u_opacity;
  uniform vec4 u_crop; // x, y, w, h（注意：y 以纹理底部为 0 原点）

  void main() {
    // 将 [0,1] 的 vUv 映射到裁剪区域内
    vec2 uv = vec2(
      u_crop.x + vUv.x * u_crop.z,
      u_crop.y + vUv.y * u_crop.w
    );

    vec4 color = texture2D(u_tex, uv);
    gl_FragColor = vec4(color.rgb, color.a * u_opacity);
  }
`

interface VideoLayerProps {
  overlay: RenderableOverlay & VideoOverlay
}

// 全屏 Three.js 视频平面组件
const VideoPlane: React.FC<{
  video: React.RefObject<HTMLVideoElement | null>
  overlay: VideoOverlay
}> = ({ video, overlay }) => {
  const vp = useThree(state => state.viewport)
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()

  const texture = useVideoTexture(video)
  const materialRef = useRef<THREE.ShaderMaterial>(null)

  // 配置纹理属性
  useMemo(() => {
    if (!texture) return
    texture.wrapS = texture.wrapT = THREE.ClampToEdgeWrapping
    texture.minFilter = THREE.LinearFilter
    texture.magFilter = THREE.LinearFilter
    texture.generateMipmaps = false
    texture.needsUpdate = true
  }, [texture])

  // 计算视频变换参数（包含坐标偏移）
  const { position, rotation } = useMemo(() => {
    // 旋转角度转换为弧度
    const rotationZ = -(overlay.rotation || 0) * Math.PI / 180

    // 以播放器左上为(0,0)，Three 以中心为(0,0)
    // 计算 Overlay 中心的像素坐标
    const cx = (overlay.left ?? 0) + overlay.width / 2
    const cy = (overlay.top ?? 0) + overlay.height / 2

    // 将像素坐标映射到 Three 世界坐标（单位：world units）
    // X: 左->右，原点居中
    const worldX = (cx - playerWidth / 2) / vp.factor
    // Y: 屏幕向下为正像素，Three 中向上为正，需要取反
    const worldY = (playerHeight / 2 - cy) / vp.factor

    return {
      position: [worldX, worldY, 0] as [number, number, number],
      rotation: [0, 0, rotationZ] as [number, number, number],
    }
  }, [overlay.left, overlay.top, overlay.width, overlay.height, overlay.rotation, playerWidth, playerHeight, vp.factor])

  // 计算裁剪区域（归一化到纹理坐标，原点在左下）
  const cropVec = useMemo(() => {
    const c = overlay.cropData
    if (!c) return new THREE.Vector4(0, 0, 1, 1)
    const nx = c.x / 100.0
    const nyTop = c.y / 100.0
    const nw = c.width / 100.0
    const nh = c.height / 100.0
    const ny = 1.0 - (nyTop + nh)
    return new THREE.Vector4(nx, ny, nw, nh)
  }, [overlay.cropData])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: texture },
      u_opacity: { value: overlay.styles.opacity ?? 1 },
      u_crop: { value: cropVec },
    }),
    [texture, overlay.styles.opacity, cropVec]
  )

  // 每帧更新透明度
  useFrame(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.u_opacity.value = overlay.styles.opacity ?? 1
    }
  })

  return (
    <mesh
      position={position}
      rotation={rotation}
    >
      <planeGeometry args={[overlay.width / vp.factor, overlay.height / vp.factor]} />
      {texture && (
        <shaderMaterial
          ref={materialRef}
          vertexShader={VERTEX_SHADER}
          fragmentShader={FRAGMENT_SHADER}
          uniforms={uniforms}
          transparent={true}
          toneMapped={false}
        />
      )}
    </mesh>
  )
}

export const VideoLayer: React.FC<VideoLayerProps> = ({ overlay }) => {
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const src = overlay.localSrc || overlay.src

  const [videoData, setVideoData] = useState<any>(null)

  useEffect(() => {
    parseMedia({ src })
      .then(data => {
        console.log(data)
        setVideoData(data)
      })
      .catch(err => console.log(err))
  }, [src])

  useEffect(() => {
    const handle = delayRender('Loading video')

    const handleLoadedMetadata = () => {
      console.log('metadata loaded')
      setVideoData(true)
      continueRender(handle)
    }

    const handleError = (_: ErrorEvent) => {
      continueRender(handle)
    }

    videoRef.current?.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoRef.current?.addEventListener('error', handleError)

    return () => {
      videoRef.current?.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoRef.current?.removeEventListener('error', handleError)
      continueRender(handle)
    }
  }, [src])

  return (
    <div
      id={`video-layer-${overlay.id}`}
      style={{
        position: 'absolute',
        left: 0,
        top: 0,
        width: playerWidth,
        height: playerHeight,
        pointerEvents: 'none'
      }}
    >
      {videoData && (
        <ThreeCanvas
          width={playerWidth}
          height={playerHeight}
          camera={{ position: [0, 0, 1] }}
          style={{ width: '100%', height: '100%', border: '4px solid red' }}
        >
          <VideoPlane video={videoRef} overlay={overlay} />
        </ThreeCanvas>
      )}

      {/* 隐藏的视频元素作为纹理源 */}
      <Video
        ref={videoRef}
        src={src}
        style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
        volume={overlay.styles.volume ?? 1}
        {...getProgressiveOverlayProps(overlay)}
      />
    </div>
  )
}
